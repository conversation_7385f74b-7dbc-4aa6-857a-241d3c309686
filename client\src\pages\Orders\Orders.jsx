import React, { useContext, useState, useEffect } from 'react';
import { StoreContext } from '../../context/StoreContext';
import axios from 'axios';
import './Orders.css';

const Orders = () => {
  const { user, token, url } = useContext(StoreContext);
  const [orders, setOrders] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);

  // Fetch user orders from API
  const fetchOrders = async () => {
    if (!token) {
      setError('Please login to view your orders');
      setLoading(false);
      return;
    }

    try {
      console.log('Fetching orders with token:', token.substring(0, 10) + '...');

      const response = await axios.post(`${url}/api/order/userorders`, {}, {
        headers: {
          Authorization: `Bearer ${token}`,
          'Content-Type': 'application/json'
        }
      });

      console.log('Orders response:', response.data);

      if (response.data.success) {
        setOrders(response.data.data || []);
      } else {
        setError(response.data.message || 'Failed to fetch orders');
      }
    } catch (error) {
      console.error('Error fetching orders:', error);
      if (error.response) {
        console.error('Response data:', error.response.data);
        setError(error.response.data.message || 'Failed to fetch orders');
      } else {
        setError('Network error. Please try again.');
      }
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchOrders();
  }, [token, url]);

  if (loading) {
    return (
      <div className="orders-container">
        <div className="orders-header">
          <h2>My Orders</h2>
        </div>
        <div className="orders-content">
          <div className="loading">
            <p>Loading your orders...</p>
          </div>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="orders-container">
        <div className="orders-header">
          <h1>My Orders</h1>
        </div>
        <div className="orders-content">
          <div className="error">
            <p>Error: {error}</p>
            <button onClick={fetchOrders}>Try Again</button>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="orders-container">
      <div className="orders-header">
        <h1>My Orders</h1>
      </div>

      <div className="orders-content">
        {orders.length > 0 ? (
          <div className="orders-list">
            {orders.map((order) => (
              <div key={order._id} className="order-card">
                <div className="order-header">
                  <div>
                    <h3>Order #{order._id.slice(-6)}</h3>
                    <p className="order-date">{new Date(order.date).toLocaleDateString()}</p>
                  </div>
                  <div className="order-status">
                    <span className={`status-badge ${order.status ? order.status.toLowerCase().replace(' ', '-') : 'pending'}`}>
                      {order.status || 'Pending'}
                    </span>
                  </div>
                </div>

                <div className="order-items">
                  {order.items && order.items.map((item, index) => (
                    <div key={item._id || index} className="order-item">
                      <div className="item-details">
                        <h4>{item.name}</h4>
                        <p>Quantity: {item.quantity}</p>
                        <p className="item-price">₹{item.price ? item.price.toFixed(2) : '0.00'}</p>
                      </div>
                    </div>
                  ))}
                </div>

                <div className="order-footer">
                  <div className="order-total">
                    <p>Total: <span>₹{order.amount ? order.amount.toFixed(2) : '0.00'}</span></p>
                  </div>
                  <div className="order-payment">
                    <span className={`payment-badge ${order.payment ? 'paid' : 'unpaid'}`}>
                      {order.payment ? 'Paid' : 'Unpaid'}
                    </span>
                  </div>
                </div>
              </div>
            ))}
          </div>
        ) : (
          <div className="no-orders">
            <div className="no-orders-icon">📦</div>
            <h2>No Orders Yet</h2>
            <p>You haven't placed any orders yet. Start ordering your favorite food!</p>
            <button className="browse-menu-button" onClick={() => window.location.href = '/#explore-menu'}>
              Browse Menu
            </button>
          </div>
        )}
      </div>
    </div>
  );
};

export default Orders;
