.order-item{
    display: grid;
    grid-template-columns: 0.5fr 2fr 1fr 1fr 1fr;
    align-items: center;
    gap: 30px;
    padding: 20px;
    border: 1px solid tomato;
    margin: 30px 0px;
    font-size: 14px;
    color: #505050;
}
.order-item-food, .order-item-name{
    font-weight:600;
}
.order-item-name{
    margin-top: 30px;
    margin-bottom: 5px;
}
.order-item-address{
    margin-bottom: 10px;
}
.order-item select{
    background-color: #ffe8e4;
    border: 1px solid tomato;
    width:max(10vw,120px);
    padding: 10px;
    outline: none;
}

@media (max-width:1000px){
    .order-item{
        font-size: 12px;
        grid-template-columns: 0.5fr 2fr 1fr ;
        padding: 15px 8px;
    }
    .order-item select{
        padding: 5px;
        font-size: 12px;
    }
    .order-item img{
        width:40px;
    }
}